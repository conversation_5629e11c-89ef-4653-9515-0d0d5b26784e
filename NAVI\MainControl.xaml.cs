﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace NAVI
{
    /// <summary>
    /// MainControl.xaml 的交互逻辑
    /// </summary>
    public partial class MainControl : UserControl
    {
        public MainControl()
        {
            InitializeComponent();
        }

        private void MenuTree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            var item = (e.NewValue as TreeViewItem);
            if (item == null || item.Tag == null) return;

            try
            {
                switch (item.Tag.ToString())
                {
                    case "BusinessData":
                        MainContent.Content = new BusinessDataControl();//事业者数据
                        break;
                    case "NationalData":
                        MainContent.Content = new NationalDataControl();//国保联数据
                        break;
                    case "ServiceCodeData":
                        MainContent.Content = new ServiceCodeDataControl();//服务代码数据
                        break;
                    case "MatchData":
                        //MainContent.Content = new MatchDataControl();
                        break;
                    case "BusinessMaster":
                        MainContent.Content = new BusinessDataControl();
                        break;

                    // 新增的处理功能控件
                    case "ProviderExcelImportControl":
                        MainContent.Content = new ProviderExcelImportControl();//受给者
                        break;
                    case "ProviderDocumentImportControl":
                        MainContent.Content = new ProviderDocumentImportControl();//受给者
                        break;
                    case "NationalCsvImportControl":
                        MainContent.Content = new NationalCsvImportControl();//国保联
                        break;
                    case "DataReconciliationControl":
                        MainContent.Content = new DataReconciliationControl();//受给者
                        break;
                    case "DataEditControl":
                        MainContent.Content = new DataEditControl();
                        break;

                    // 数据库相关控件映射
                    case "ProviderManagementControl":
                        MainContent.Content = new BusinessDataControl();//事业者
                        break;
                    case "RecipientServiceInfoControl":
                        MainContent.Content = new RecipientServiceInfoControl();//受给者用户信息
                        break;
                    case "NationalHealthDataControl":
                        MainContent.Content = new NationalDataControl();//
                        break;
                    case "ReconciliationResultControl":
                        MainContent.Content = new ReconciliationResultControl();//受给者对比合照数据
                        break;
                    case "MonthlyProcessingControl":
                        MainContent.Content = new MonthlyProcessingControl();//月账单
                        break;
                    case "UserInfoData":
                        MainContent.Content = new UserManagementControl();//用户信息
                        break;

                    // 出力机能控件
                    case "FinanceExport":
                        MainContent.Content = new FinanceCsvExportControl();//财务CSV输出
                        break;
                    case "SubsidyExport":
                        MainContent.Content = new SubsidyCsvExportControl();//补助金CSV输出
                        break;

                    // 其他case可按需添加
                    default:
                        MainContent.Content = null;
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载页面失败：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
